# Kaeru Redirect Server

This is the Discord Linked Roles server for the Kaeru bot system. It handles OAuth2 authentication and metadata updates for Discord's linked roles feature.

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Copy the environment template
cp .env.template .env

# Edit the .env file with your values
nano .env
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Start the Server

```bash
# Development
npm run dev

# Production
npm start
```

## 📋 Environment Variables

| Variable         | Description                       | Required | Example                                                  |
| ---------------- | --------------------------------- | -------- | -------------------------------------------------------- |
| `MONGO_URI`      | MongoDB connection string         | ✅       | `mongodb+srv://user:<EMAIL>/kaeru`      |
| `CLIENT_ID`      | Discord application client ID     | ✅       | `123456789012345678`                                     |
| `CLIENT_TOKEN`   | Discord bot token                 | ✅       | `your_bot_token_here`                                    |
| `CLIENT_SECRET`  | Discord application client secret | ✅       | `your_secret_here`                                       |
| `REDIRECT_URI`   | OAuth2 redirect URI               | ✅       | `https://redirect.yourdomain.com/discord-oauth-callback` |
| `COOKIE_SECRET`  | Secret for signing cookies        | ✅       | `random_secret_string`                                   |
| `PORT`           | Server port                       | ❌       | `3000` (default)                                         |
| `NODE_ENV`       | Environment mode                  | ❌       | `production`                                             |
| `BOT_OWNER_ID`   | Bot owner's Discord ID            | ✅       | `123456789012345678`                                     |
| `APP_SERVER_URL` | URL of this server                | ✅       | `https://redirect.yourdomain.com`                        |

## 🔗 Integration with Main Bot

### Database Sharing

-   **IMPORTANT**: Use the same `MONGO_URI` as your main Kaeru bot
-   Both systems access the same `UserData` collection
-   Timelapse and ticket counts are shared between systems

### API Endpoints

The main bot communicates with this server via these endpoints:

-   `POST /increment-timelapse` - Increments user's timelapse count
-   `POST /update-metadata` - Updates user's Discord metadata
-   `GET /linked-role` - Initiates OAuth2 flow

### Discord Bot Configuration

In your main bot's environment, set:

```env
APP_SERVER_URL=https://kaeru-minesa.example-redirect.com
```

## 🏗️ Architecture

```
┌─────────────────┐    HTTP API    ┌──────────────────┐
│   Kaeru Bot     │◄──────────────►│ Redirect Server  │
│   (Discord)     │                │  (This Repo)     │
└─────────────────┘                └──────────────────┘
         │                                   │
         │            Shared Database        │
         └──────────────►MongoDB◄────────────┘
```

## 📊 Linked Roles Metadata

The server manages these metadata fields:

| Field           | Type    | Description                                                            |
| --------------- | ------- | ---------------------------------------------------------------------- |
| `time_master`   | Boolean | `true` if user has completed 10+ timelapse activities                  |
| `issue_tracker` | Integer | Number of tickets opened by the user (allows flexible role thresholds) |
