:root {
	/* Base Colors */
	--primary-color: #b38d70;
	--base-black: #292524;
	--base-gray: #9e9894e6;

	/* Leaf Colors */
	--leaf-color-1: #fd9b62;
	--leaf-color-2: #fdc562;

	/* Backgrounds */
	--background-body: #fff1e0;
	--background-button: #2925240d;

	/* Selection Color */
	--selection-color: #2925241a;

	/* Label */
	--label-color: #1c191799;
	--label-background: #1c191705;
	--label-border: #1c191726;

	/* Fonts */
	--font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
		"Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

::selection {
	background: var(--selection-color);
}

body {
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
	font-family: var(--font-family);
	background: var(--background-body);
	color: var(--base-black);
	min-height: 100vh;
}

header {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 2rem;
	padding: 1.75rem 0;
}

main {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	gap: 4rem;
	padding: 0 2rem;
}

main section {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 4rem;
}

main section .kaeru_mascot {
	width: 20rem;
	height: auto;
}

main section .kaeru_sticker {
	width: 10rem;
	height: auto;
}

main section .verified_icon {
	width: 2.5rem;
	height: auto;
}

main section .trusted {
	width: 10rem;
	height: auto;
}

button {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 1rem;
	padding: 1rem 2rem;
	border: none;
	border-radius: 1rem;
	background: var(--background-button);
	color: var(--base-black);
	font-size: 1.5rem;
	font-weight: 700;
	cursor: pointer;
	transition: background 0.2s ease;
}

footer .big-text {
	position: absolute;
	bottom: 10%;
	left: 50%;
	transform: translateX(-50%);
	font-size: 14rem;
	font-weight: 900;
	color: transparent;
	background: linear-gradient(to bottom, #b38d701a 50%, #b38d7000);
	-webkit-background-clip: text;
	background-clip: text;
	pointer-events: none;
	user-select: none;
	overflow: hidden;
	height: 9rem;
}

img.ellipses {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: auto;
	pointer-events: none;
	z-index: -1;
}
