:root {
	/* Base Colors */
	--primary-color: #b38d70;
	--base-black: #292524;
	--base-gray: #9e9894e6;

	/* Leaf Colors */
	--leaf-color-1: #fd9b62;
	--leaf-color-2: #fdc562;

	/* Backgrounds */
	--background-body: #fff1e0;
	--background-button: #2925240d;

	/* Selection Color */
	--selection-color: #2925241a;

	/* Label */
	--label-color: #1c191799;
	--label-background: #1c191705;
	--label-border: #1c191726;

	/* Fonts */
	--font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
		"Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

::selection {
	background: var(--selection-color);
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: center;
	font-family: var(--font-family);
	background: var(--background-body);
	color: var(--base-black);
	min-height: 100vh;
	overflow-x: hidden;
}

header {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 2rem;
	padding: 1.75rem 0;
}

main {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	gap: 4rem;
	padding: 0 2rem;
}

main section {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 4rem;
	max-width: 1200px;
	width: 100%;
}

main section > div {
	display: flex;
	align-items: center;
	gap: 2rem;
	background: rgba(255, 255, 255, 0.8);
	border: 2px solid rgba(185, 141, 112, 0.3);
	border-radius: 1.5rem;
	padding: 2rem;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10px);
}

main section .kaeru_mascot {
	width: 20rem;
	height: auto;
	border-radius: 1rem;
}

main section .kaeru_sticker {
	position: absolute;
	right: -2rem;
	top: 50%;
	transform: translateY(-50%) rotate(15deg);
	width: 10rem;
	height: auto;
	z-index: 2;
	transition: transform 0.3s ease;
}

main section .kaeru_sticker:hover {
	transform: translateY(-50%) rotate(25deg) scale(1.1);
}

main section .verified_icon {
	width: 2.5rem;
	height: auto;
}

main section .trusted {
	position: absolute;
	left: -3rem;
	top: -2rem;
	transform: rotate(-15deg);
	width: 10rem;
	height: auto;
	z-index: 2;
	transition: transform 0.3s ease;
}

main section .trusted:hover {
	transform: rotate(-25deg) scale(1.1);
}

main section div[role="text"] h1 {
	font-size: 2.5rem;
	font-weight: 800;
	margin: 0 0 0.5rem 0;
	color: var(--base-black);
}

main section div[role="text"] h1 span {
	color: var(--primary-color);
}

main section div[role="text"] p {
	font-size: 1.2rem;
	color: var(--base-gray);
	margin: 0;
	font-weight: 500;
}

button {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 1rem;
	padding: 1rem 2rem;
	border: none;
	border-radius: 1rem;
	background: var(--background-button);
	color: var(--base-black);
	font-size: 1.5rem;
	font-weight: 700;
	cursor: pointer;
	transition: background 0.2s ease;
	max-width: 400px;
	width: 100%;
}

button:hover {
	background: rgba(41, 37, 36, 0.1);
}

footer .big-text {
	position: absolute;
	bottom: 10%;
	left: 50%;
	transform: translateX(-50%);
	font-size: 14rem;
	font-weight: 900;
	color: transparent;
	background: linear-gradient(to bottom, #b38d701a 50%, #b38d7000);
	-webkit-background-clip: text;
	background-clip: text;
	pointer-events: none;
	user-select: none;
	overflow: hidden;
	height: 9rem;
}

img.ellipses {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: auto;
	pointer-events: none;
	z-index: -1;
}

/* Responsive Design */
@media (max-width: 1024px) {
	main section {
		gap: 2rem;
		padding: 0 1rem;
	}

	main section .kaeru_mascot {
		width: 16rem;
	}

	main section .kaeru_sticker {
		width: 8rem;
		right: -1rem;
	}

	main section .trusted {
		width: 8rem;
		left: -2rem;
	}

	footer .big-text {
		font-size: 10rem;
		height: 7rem;
	}
}

@media (max-width: 768px) {
	header {
		gap: 1rem;
		padding: 1rem 0;
	}

	header svg {
		width: 50px;
		height: 50px;
	}

	main {
		gap: 2rem;
		padding: 0 1rem;
	}

	main section {
		flex-direction: column;
		gap: 1.5rem;
		position: relative;
	}

	main section > div {
		flex-direction: column;
		text-align: center;
		gap: 1.5rem;
		padding: 1.5rem;
		margin: 0 1rem;
	}

	main section .kaeru_mascot {
		width: 12rem;
	}

	main section .kaeru_sticker {
		position: static;
		transform: none;
		width: 6rem;
		margin-top: 1rem;
	}

	main section .trusted {
		position: static;
		transform: none;
		width: 6rem;
		margin-bottom: 1rem;
	}

	main section div[role="text"] h1 {
		font-size: 2rem;
	}

	main section div[role="text"] p {
		font-size: 1rem;
	}

	button {
		font-size: 1.2rem;
		padding: 0.8rem 1.5rem;
	}

	footer .big-text {
		font-size: 6rem;
		height: 4rem;
		bottom: 5%;
	}
}

@media (max-width: 480px) {
	header {
		gap: 0.5rem;
	}

	header svg {
		width: 40px;
		height: 40px;
	}

	main section > div {
		margin: 0 0.5rem;
		padding: 1rem;
	}

	main section .kaeru_mascot {
		width: 10rem;
	}

	main section .kaeru_sticker {
		width: 5rem;
	}

	main section .trusted {
		width: 5rem;
	}

	main section div[role="text"] h1 {
		font-size: 1.5rem;
	}

	main section div[role="text"] p {
		font-size: 0.9rem;
	}

	button {
		font-size: 1rem;
		padding: 0.7rem 1rem;
	}

	footer .big-text {
		font-size: 4rem;
		height: 3rem;
	}
}
