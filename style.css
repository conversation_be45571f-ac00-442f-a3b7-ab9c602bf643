:root {
	/* Base Colors */
	--primary-color: #b38d70;
	--base-black: #292524;
	--base-gray: #9e9894e6;

	/* Leaf Colors */
	--leaf-color-1: #fd9b62;
	--leaf-color-2: #fdc562;

	/* Backgrounds */
	--background-body: #fff1e0;
	--background-button: #2925240d;

	/* Selection Color */
	--selection-color: #2925241a;

	/* Label */
	--label-color: #1c191799;
	--label-background: #1c191705;
	--label-border: #1c191726;

	/* Fonts */
	--font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
		"Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

::selection {
	background: var(--selection-color);
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

html,
body {
	height: 100vh;
	overflow: hidden;
}

body {
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	font-family: var(--font-family);
	background: var(--background-body);
	color: var(--base-black);
}

header {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 2rem;
	padding: 1rem 0;
	flex-shrink: 0;
}

main {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	gap: 3rem;
	flex: 1;
	width: 100%;
	padding: 0;
}

main section {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	max-width: 1200px;
	width: 100%;
}

/* Main container for the image and text overlay */
main section > div {
	position: relative;
	width: 580px;
	height: 349px;
}

/* Text container positioned over the image */
main section div[role="text"] {
	position: absolute;
	left: 180px;
	top: 300px;
	width: 380px;
	height: 130px;
	background: #fff0de;
	border: 1px solid #decab1;
	box-shadow: 0px 97px 39px rgba(0, 0, 0, 0.01),
		0px 54px 33px rgba(0, 0, 0, 0.05), 0px 24px 24px rgba(0, 0, 0, 0.09),
		0px 6px 13px rgba(0, 0, 0, 0.1);
	border-radius: 19px;
	transform: rotate(4.01deg);
	padding: 25px;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 8px;
	z-index: 3;
}

main section .kaeru_mascot {
	position: absolute;
	width: 400px;
	left: 50px;
	top: 0px;
	object-fit: cover;
}

main section .kaeru_sticker {
	position: absolute;
	right: -2rem;
	top: 50%;
	transform: translateY(50%) translateX(70%) scale(0.7) rotate(15deg);
	width: 10rem;
	height: auto;
	z-index: 2;
	transition: transform 0.3s ease;
}

main section .kaeru_sticker:hover {
	transform: translateY(50%) translateX(70%) scale(0.55) rotate(15deg);
}

main section .verified_icon {
	width: 2.5rem;
	height: auto;
}

main section .trusted {
	position: absolute;
	left: -50%;
	top: 20%;
	transform: rotate(-15deg) scale(0.8);
	width: 10rem;
	height: auto;
	z-index: 2;
	transition: transform 0.3s ease;
}

main section .trusted:hover {
	transform: rotate(-25deg) scale(1);
}

main section div[role="text"] h1 {
	width: 100%;
	font-family: var(--font-family);
	font-style: normal;
	font-weight: 700;
	font-size: 32px;
	line-height: 36px;
	letter-spacing: -0.025em;
	color: var(--base-black);
	margin: 0;
	flex: none;
	order: 0;
	align-self: stretch;
	flex-grow: 0;
}

main section div[role="text"] h1 span {
	background: linear-gradient(
		90deg,
		rgba(102, 91, 82, 0.9) 0%,
		#ff980767 100%
	);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

main section div[role="text"] p {
	width: 100%;
	font-family: var(--font-family);
	font-style: normal;
	font-weight: 700;
	font-size: 16px;
	line-height: 20px;
	letter-spacing: -0.025em;
	color: #000000;
	margin: 0;
	flex: none;
	order: 1;
	align-self: stretch;
	flex-grow: 0;
}

button {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 1rem;
	padding: 1rem 2rem;
	border: none;
	border-radius: 1rem;
	background: var(--background-button);
	color: var(--base-black);
	font-size: 1.5rem;
	font-weight: 700;
	cursor: pointer;
	transition: background 0.2s ease;
}

button:hover {
	background: rgba(41, 37, 36, 0.1);
}

button:hover .verified_icon {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

footer {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	pointer-events: none;
	z-index: 1;
}

footer .big-text {
	position: absolute;
	left: 50%;
	bottom: 1%;
	transform: translateX(-50%);
	font-size: 12rem;
	font-weight: 900;
	color: transparent;
	background: linear-gradient(to bottom, #b38d701a 50%, #b38d7000);
	-webkit-background-clip: text;
	background-clip: text;
	pointer-events: none;
	user-select: none;
	overflow: hidden;
	height: 8rem;
}

img.ellipses {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: auto;
	pointer-events: none;
	z-index: -1;
}

/* Responsive Design */
@media (max-width: 1024px) {
	main {
		gap: 2rem;
	}

	main section {
		gap: 2rem;
	}

	main section > div {
		width: 450px;
		height: 280px;
	}

	main section .kaeru_mascot {
		width: 240px;
		height: 240px;
	}

	main section div[role="text"] {
		left: 140px;
		top: 160px;
		width: 290px;
		height: 100px;
		padding: 20px;
	}

	main section div[role="text"] h1 {
		font-size: 26px;
		line-height: 30px;
	}

	main section div[role="text"] p {
		font-size: 14px;
		line-height: 18px;
	}

	main section .kaeru_sticker {
		width: 8rem;
		right: -1rem;
	}

	main section .trusted {
		width: 8rem;
		left: -2rem;
	}

	footer .big-text {
		font-size: 8rem;
		height: 6rem;
	}
}

@media (max-width: 768px) {
	header {
		gap: 1rem;
		padding: 0.5rem 0;
	}

	header svg {
		width: 50px;
		height: 50px;
	}

	main {
		gap: 1.5rem;
	}

	main section {
		flex-direction: column;
		gap: 1.5rem;
		position: relative;
	}

	main section > div {
		width: 380px;
		height: 220px;
		margin: 0 auto;
	}

	main section .kaeru_mascot {
		width: 200px;
		height: 200px;
	}

	main section div[role="text"] {
		left: 100px;
		top: 120px;
		width: 260px;
		height: 90px;
		padding: 15px;
		transform: rotate(2deg);
	}

	main section div[role="text"] h1 {
		font-size: 20px;
		line-height: 24px;
	}

	main section div[role="text"] p {
		font-size: 13px;
		line-height: 16px;
	}

	main section .kaeru_sticker {
		position: absolute;
		right: -1rem;
		top: 50%;
		transform: translateY(-50%) rotate(10deg);
		width: 6rem;
	}

	main section .trusted {
		position: absolute;
		left: -1.5rem;
		top: -1rem;
		transform: rotate(-10deg);
		width: 6rem;
	}

	button {
		font-size: 1.2rem;
		padding: 0.8rem 1.5rem;
	}

	footer .big-text {
		font-size: 5rem;
		height: 4rem;
	}
}

@media (max-width: 480px) {
	header {
		gap: 0.5rem;
		padding: 0.25rem 0;
	}

	header svg {
		width: 40px;
		height: 40px;
	}

	main {
		gap: 1rem;
	}

	main section > div {
		width: 320px;
		height: 180px;
		margin: 0 auto;
	}

	main section .kaeru_mascot {
		width: 160px;
		height: 160px;
	}

	main section div[role="text"] {
		left: 80px;
		top: 90px;
		width: 220px;
		height: 80px;
		padding: 12px;
		transform: rotate(1deg);
	}

	main section div[role="text"] h1 {
		font-size: 18px;
		line-height: 20px;
	}

	main section div[role="text"] p {
		font-size: 12px;
		line-height: 15px;
	}

	main section .kaeru_sticker {
		width: 5rem;
		right: -0.5rem;
	}

	main section .trusted {
		width: 5rem;
		left: -1rem;
	}

	button {
		font-size: 1rem;
		padding: 0.7rem 1rem;
	}

	footer .big-text {
		font-size: 4rem;
		height: 3rem;
	}
}
